'use client';

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { Icon } from '@/components/Icon';
import {
  iconLibraries,
  fonts,
  colorThemes,
  IconLibrary,
  FontFamily,
  ColorTheme
} from '@/lib/themes';

interface ThemeCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ThemeCustomizer = React.memo(function ThemeCustomizer({ isOpen, onClose }: ThemeCustomizerProps) {
  const {
    iconLibrary,
    font,
    colorTheme,
    setIconLibrary,
    setFont,
    setColorTheme,
    resetToDefaults
  } = useTheme();

  const [activeTab, setActiveTab] = useState<'icons' | 'fonts' | 'colors'>('icons');
  const popupRef = useRef<HTMLDivElement>(null);

  // Memoize expensive computations
  const fontsByCategory = useMemo(() => {
    const sansSerif = Object.entries(fonts).filter(([_, fontConfig]) => fontConfig.category === 'sans');
    const monospace = Object.entries(fonts).filter(([_, fontConfig]) => fontConfig.category === 'mono');
    return { sansSerif, monospace };
  }, []);

  const colorThemeEntries = useMemo(() => Object.entries(colorThemes), []);
  const iconLibraryEntries = useMemo(() => Object.entries(iconLibraries), []);

  // Memoized event handlers to prevent re-creation
  const handleEscape = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen) {
      onClose();
    }
  }, [isOpen, onClose]);

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
      onClose();
    }
  }, [onClose]);

  // Optimized event listeners with passive option
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleEscape, { passive: true });
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, handleEscape]);

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside, { passive: true });
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, handleClickOutside]);

  if (!isOpen) return null;

  return (
    <div
      ref={popupRef}
      className="theme-customizer fixed top-4 right-4 w-96 max-h-[calc(100vh-2rem)] bg-white rounded-lg shadow-2xl z-50 overflow-hidden popup-enter"
      style={{
        backgroundColor: 'var(--color-card)',
        color: 'var(--color-card-foreground)',
        border: '1px solid var(--color-border)'
      }}
    >
        {/* Header */}
        <div
          className="px-4 py-3 border-b flex items-center justify-between"
          style={{ borderColor: 'var(--color-border)' }}
        >
          <div className="flex items-center space-x-2">
            <Icon name="palette" size={20} />
            <h2 className="text-lg font-semibold">Themes</h2>
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={resetToDefaults}
              className="px-2 py-1 text-xs rounded border hover:opacity-80 transition-opacity"
              style={{
                borderColor: 'var(--color-border)',
                color: 'var(--color-muted-foreground)'
              }}
              title="Reset to Defaults"
            >
              Reset
            </button>
            <button
              onClick={onClose}
              className="p-1 hover:opacity-80 transition-opacity"
              style={{ color: 'var(--color-muted-foreground)' }}
              title="Close"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div
          className="flex border-b"
          style={{ borderColor: 'var(--color-border)' }}
        >
          {[
            { id: 'icons', label: 'Icons', icon: 'folder' },
            { id: 'fonts', label: 'Fonts', icon: 'type' },
            { id: 'colors', label: 'Colors', icon: 'palette' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex items-center justify-center space-x-1 px-3 py-2 border-b-2 transition-colors text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500'
                  : 'border-transparent hover:bg-gray-50'
              }`}
              style={{
                backgroundColor: activeTab === tab.id ? 'var(--color-accent)' : 'transparent',
                color: activeTab === tab.id ? 'var(--color-accent-foreground)' : 'var(--color-foreground)'
              }}
            >
              <Icon name={tab.icon as any} size={14} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-[calc(100vh-12rem)]">
          {/* Icons Tab */}
          {activeTab === 'icons' && (
            <div>
              <h3 className="text-base font-medium mb-3">Icon Library</h3>
              <div className="space-y-3">
                {iconLibraryEntries.map(([key, lib]) => (
                  <div
                    key={key}
                    onClick={() => setIconLibrary(key as IconLibrary)}
                    className={`p-3 rounded border cursor-pointer transition-all hover:shadow-sm ${
                      iconLibrary === key
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    style={{
                      borderColor: iconLibrary === key ? 'var(--color-primary)' : 'var(--color-border)',
                      backgroundColor: iconLibrary === key ? 'var(--color-accent)' : 'transparent'
                    }}
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <Icon name="folder" size={16} library={key as IconLibrary} />
                      <Icon name="file-image" size={16} library={key as IconLibrary} />
                      <Icon name="file-video" size={16} library={key as IconLibrary} />
                      <Icon name="file-code" size={16} library={key as IconLibrary} />
                    </div>
                    <h4 className="font-medium text-sm">{lib.name}</h4>
                    <p
                      className="text-xs"
                      style={{ color: 'var(--color-muted-foreground)' }}
                    >
                      {lib.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Fonts Tab */}
          {activeTab === 'fonts' && (
            <div>
              <h3 className="text-base font-medium mb-3">Font Family</h3>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium mb-2 text-sm">Sans-serif</h4>
                  <div className="space-y-2">
                    {fontsByCategory.sansSerif.map(([key, fontConfig]) => (
                        <div
                          key={key}
                          onClick={() => setFont(key as FontFamily)}
                          className={`p-2 rounded border cursor-pointer transition-all hover:shadow-sm ${
                            font === key
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          style={{
                            borderColor: font === key ? 'var(--color-primary)' : 'var(--color-border)',
                            backgroundColor: font === key ? 'var(--color-accent)' : 'transparent',
                            fontFamily: `var(--${fontConfig.variable})`
                          }}
                        >
                          <div className="font-medium text-sm">{fontConfig.name}</div>
                          <div
                            className="text-xs"
                            style={{ color: 'var(--color-muted-foreground)' }}
                          >
                            The quick brown fox
                          </div>
                        </div>
                      ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2 text-sm">Monospace</h4>
                  <div className="space-y-2">
                    {fontsByCategory.monospace.map(([key, fontConfig]) => (
                        <div
                          key={key}
                          onClick={() => setFont(key as FontFamily)}
                          className={`p-2 rounded border cursor-pointer transition-all hover:shadow-sm ${
                            font === key
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          style={{
                            borderColor: font === key ? 'var(--color-primary)' : 'var(--color-border)',
                            backgroundColor: font === key ? 'var(--color-accent)' : 'transparent',
                            fontFamily: `var(--${fontConfig.variable})`
                          }}
                        >
                          <div className="font-medium text-sm">{fontConfig.name}</div>
                          <div
                            className="text-xs"
                            style={{ color: 'var(--color-muted-foreground)' }}
                          >
                            const hello = "world";
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Colors Tab */}
          {activeTab === 'colors' && (
            <div>
              <h3 className="text-base font-medium mb-3">Color Theme</h3>
              <div className="space-y-2">
                {colorThemeEntries.map(([key, theme]) => (
                  <div
                    key={key}
                    onClick={() => setColorTheme(key as ColorTheme)}
                    className={`p-3 rounded border cursor-pointer transition-all hover:shadow-sm ${
                      colorTheme === key
                        ? 'border-blue-500'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    style={{
                      borderColor: colorTheme === key ? 'var(--color-primary)' : 'var(--color-border)'
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{theme.name}</h4>
                      <div className="flex items-center space-x-1">
                        <div
                          className="w-3 h-3 rounded"
                          style={{ backgroundColor: theme.colors.background }}
                        />
                        <div
                          className="w-3 h-3 rounded"
                          style={{ backgroundColor: theme.colors.primary }}
                        />
                        <div
                          className="w-3 h-3 rounded"
                          style={{ backgroundColor: theme.colors.accent }}
                        />
                      </div>
                    </div>
                    <div
                      className="text-xs p-2 rounded"
                      style={{
                        backgroundColor: theme.colors.background,
                        color: theme.colors.foreground,
                        border: `1px solid ${theme.colors.border}`
                      }}
                    >
                      Preview text
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
    </div>
  );
});

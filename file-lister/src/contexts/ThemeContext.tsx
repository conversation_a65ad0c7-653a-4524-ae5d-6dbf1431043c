'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { IconLibrary, FontFamily, ColorTheme, defaultTheme, colorThemes, fonts } from '@/lib/themes';
import { loadFont, getFontFamily, preloadEssentialFonts } from '@/lib/fontLoader';

interface ThemeContextType {
  iconLibrary: IconLibrary;
  font: FontFamily;
  colorTheme: ColorTheme;
  setIconLibrary: (library: IconLibrary) => void;
  setFont: (font: FontFamily) => void;
  setColorTheme: (theme: ColorTheme) => void;
  resetToDefaults: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [iconLibrary, setIconLibraryState] = useState<IconLibrary>(defaultTheme.iconLibrary);
  const [font, setFontState] = useState<FontFamily>(defaultTheme.font);
  const [colorTheme, setColorThemeState] = useState<ColorTheme>(defaultTheme.colorTheme);

  // Load theme from localStorage on mount and initialize font system
  useEffect(() => {
    // Initialize font loading system
    preloadEssentialFonts();

    const savedTheme = localStorage.getItem('file-lister-theme');
    if (savedTheme) {
      try {
        const parsed = JSON.parse(savedTheme);
        setIconLibraryState(parsed.iconLibrary || defaultTheme.iconLibrary);
        setFontState(parsed.font || defaultTheme.font);
        setColorThemeState(parsed.colorTheme || defaultTheme.colorTheme);
      } catch (error) {
        console.error('Failed to parse saved theme:', error);
      }
    }
  }, []);

  // Apply theme changes to document
  useEffect(() => {
    const theme = colorThemes[colorTheme];
    const fontConfig = fonts[font];

    // Apply CSS custom properties for colors
    const root = document.documentElement;
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    // Apply font family with dynamic loading
    const applyFont = async () => {
      try {
        // Load the font dynamically if it's not Inter (which is already loaded)
        if (font !== 'inter') {
          await loadFont(font);
        }

        // Get the font family string
        const fontFamily = getFontFamily(font);

        // Apply font to both CSS custom property and body element
        root.style.setProperty('--font-family', fontFamily);
        document.body.style.fontFamily = fontFamily;
      } catch (error) {
        console.error('Error applying font:', error);
        // Fallback to Inter if font loading fails
        const fallbackFont = getFontFamily('inter');
        root.style.setProperty('--font-family', fallbackFont);
        document.body.style.fontFamily = fallbackFont;
      }
    };

    applyFont();

    // Apply theme class to body
    document.body.className = document.body.className
      .split(' ')
      .filter(cls => !cls.startsWith('theme-'))
      .concat(`theme-${colorTheme}`)
      .join(' ');

    // Save to localStorage
    localStorage.setItem('file-lister-theme', JSON.stringify({
      iconLibrary,
      font,
      colorTheme,
    }));
  }, [iconLibrary, font, colorTheme]);

  const setIconLibrary = (library: IconLibrary) => {
    setIconLibraryState(library);
  };

  const setFont = (newFont: FontFamily) => {
    setFontState(newFont);
  };

  const setColorTheme = (theme: ColorTheme) => {
    setColorThemeState(theme);
  };

  const resetToDefaults = () => {
    setIconLibraryState(defaultTheme.iconLibrary);
    setFontState(defaultTheme.font);
    setColorThemeState(defaultTheme.colorTheme);
  };

  return (
    <ThemeContext.Provider
      value={{
        iconLibrary,
        font,
        colorTheme,
        setIconLibrary,
        setFont,
        setColorTheme,
        resetToDefaults,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
}

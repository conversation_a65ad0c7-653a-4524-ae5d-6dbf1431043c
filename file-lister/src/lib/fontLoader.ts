// Dynamic font loading utility
// This loads Google Fonts on-demand to avoid preloading all fonts at once

interface FontConfig {
  family: string;
  weights?: string[];
  display?: string;
}

const fontConfigs: Record<string, FontConfig> = {
  'inter': {
    family: 'Inter',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  },
  'roboto': {
    family: 'Roboto',
    weights: ['300', '400', '500', '700'],
    display: 'swap'
  },
  'poppins': {
    family: 'Poppins',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  },
  'open-sans': {
    family: 'Open Sans',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  },
  'lato': {
    family: 'Lato',
    weights: ['300', '400', '700'],
    display: 'swap'
  },
  'montserrat': {
    family: '<PERSON><PERSON>rat',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  },
  'source-sans': {
    family: 'Source Sans 3',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  },
  'nunito': {
    family: 'Nunito',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  },
  'geist-sans': {
    family: 'Geist',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  },
  'geist-mono': {
    family: 'Geist Mono',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  },
  'jetbrains-mono': {
    family: 'JetBrains Mono',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  },
  'fira-code': {
    family: 'Fira Code',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  },
  'source-code-pro': {
    family: 'Source Code Pro',
    weights: ['300', '400', '500', '600', '700'],
    display: 'swap'
  }
};

// Track which fonts have been loaded to avoid duplicate loading
const loadedFonts = new Set<string>();

/**
 * Dynamically load a Google Font
 */
export async function loadFont(fontKey: string): Promise<void> {
  // Skip if font is already loaded
  if (loadedFonts.has(fontKey)) {
    return;
  }

  const config = fontConfigs[fontKey];
  if (!config) {
    console.warn(`Font configuration not found for: ${fontKey}`);
    return;
  }

  try {
    // Create Google Fonts URL
    const weights = config.weights?.join(',') || '400';
    const fontUrl = `https://fonts.googleapis.com/css2?family=${encodeURIComponent(config.family)}:wght@${weights}&display=${config.display || 'swap'}`;

    // Create and append link element
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = fontUrl;
    link.crossOrigin = 'anonymous';

    // Add to document head
    document.head.appendChild(link);

    // Wait for font to load
    await new Promise<void>((resolve, reject) => {
      link.onload = () => {
        loadedFonts.add(fontKey);
        resolve();
      };
      link.onerror = () => {
        reject(new Error(`Failed to load font: ${config.family}`));
      };
    });

    console.log(`Font loaded successfully: ${config.family}`);
  } catch (error) {
    console.error(`Error loading font ${config.family}:`, error);
  }
}

/**
 * Get the CSS font family string for a font key
 */
export function getFontFamily(fontKey: string): string {
  const config = fontConfigs[fontKey];
  if (!config) {
    return 'Inter, sans-serif'; // Default fallback
  }

  // Return font family with appropriate fallback
  const isMonospace = ['geist-mono', 'jetbrains-mono', 'fira-code', 'source-code-pro'].includes(fontKey);
  const fallback = isMonospace ? 'monospace' : 'sans-serif';
  
  return `"${config.family}", ${fallback}`;
}

/**
 * Check if a font is already loaded
 */
export function isFontLoaded(fontKey: string): boolean {
  return loadedFonts.has(fontKey);
}

/**
 * Preload essential fonts (Inter is already loaded via Next.js)
 */
export function preloadEssentialFonts(): void {
  // Mark Inter as loaded since it's loaded via Next.js
  loadedFonts.add('inter');
}
